{"Version": 1, "WorkspaceRootPath": "E:\\Project\\Unity\\zhuifang\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\Project\\Unity\\zhuifang\\Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Runtime\\RendererFeatures\\PerObjectShadowFeature.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Runtime\\RendererFeatures\\PerObjectShadowFeature.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\Project\\Unity\\zhuifang\\Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Shaders\\Toon\\CharacterPBRToon.shader||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Shaders\\Toon\\CharacterPBRToon.shader||{3B902123-F8A7-4915-9F01-361F908088D0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "CharacterPBRToon.shader", "DocumentMoniker": "E:\\Project\\Unity\\zhuifang\\Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Shaders\\Toon\\CharacterPBRToon.shader", "RelativeDocumentMoniker": "Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Shaders\\Toon\\CharacterPBRToon.shader", "ToolTip": "E:\\Project\\Unity\\zhuifang\\Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Shaders\\Toon\\CharacterPBRToon.shader", "RelativeToolTip": "Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Shaders\\Toon\\CharacterPBRToon.shader", "ViewState": "AgIAAEIAAAAAAAAAAAAqwDgBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002777|", "WhenOpened": "2025-05-07T09:24:46.414Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "PerObjectShadowFeature.cs", "DocumentMoniker": "E:\\Project\\Unity\\zhuifang\\Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Runtime\\RendererFeatures\\PerObjectShadowFeature.cs", "RelativeDocumentMoniker": "Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Runtime\\RendererFeatures\\PerObjectShadowFeature.cs", "ToolTip": "E:\\Project\\Unity\\zhuifang\\Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Runtime\\RendererFeatures\\PerObjectShadowFeature.cs", "RelativeToolTip": "Library\\PackageCache\\com.unity.render-pipelines.danbaidong@8a11e37e69\\Runtime\\RendererFeatures\\PerObjectShadowFeature.cs", "ViewState": "AgIAACcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:23:12.653Z", "IsPinned": true, "EditorCaption": ""}]}]}]}