ChannelMixerControlView {
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 4px;
    padding-bottom: 4px;
}

ChannelMixerControlView > Label {
    margin-left: 0;
    margin-right: 0;
    cursor: slide-arrow;
    -unity-text-align : middle-left;
}

ChannelMixerControlView > #buttonPanel {
    flex-direction: row;
    flex-grow: 1;
    margin-bottom: 6px;
}

ChannelMixerControlView > #buttonPanel > Button {
    flex-grow: 1;
    margin-left: 1;
    margin-right: 1;
    align-items: center;
    border-left-width: 0;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 1;
}

ChannelMixerControlView > #buttonPanel > Button > Label {
    flex-grow: 1;
    -unity-text-align : middle-left;
}

ChannelMixerControlView > #sliderPanel {
    flex-direction: row;
    flex-grow: 1;
}

ChannelMixerControlView > #sliderPanel > Label {
    -unity-text-align : middle-left;
    min-width: 20px;
}

ChannelMixerControlView > #sliderPanel > Slider {
    flex-grow: 1;
    overflow:visible;
}

ChannelMixerControlView > #sliderPanel > Slider > .unity-base-field__input {
}

ChannelMixerControlView > #sliderPanel > FloatField {
    margin-right: 0;
    padding-right: 0;
    width: 40px;
}

ChannelMixerControlView > #sliderPanel > FloatField > .unity-base-field__input {
    -unity-text-align : middle-left;
}
