SliderControlView {
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 4px;
    padding-bottom: 4px;
}

SliderControlView > #SliderPanel {
    padding-bottom: 4px;
    flex-direction: row;
    flex-grow: 1;
}

.unity-slider,
.unity-slider .unity-base-field__input {
    margin-left: 0;
    margin-right: 0;
    flex-grow: 1;
    min-width: 164px;
    overflow:visible;
}

SliderControlView > #SliderPanel > FloatField {
    width: 40px;
    padding-right: 0;
    margin-right: 0;
    -unity-text-align: middle-left;
}

SliderControlView > #FieldsPanel {
    flex-direction: row;
    flex-grow: 1;
    margin-left: 0;
    margin-right: 0;
}

SliderControlView > #FieldsPanel > Label {
    -unity-text-align: middle-left;
    margin-right: 8px;
    padding-left: 4px;
}

SliderControlView > #FieldsPanel > Float<PERSON>ield {
    margin-left: 0;
    margin-right: 0;
    padding-right: 0;
    flex-direction: row;
    flex-grow: 1;
    width: 40px;
    -unity-text-align: middle-left;
}
