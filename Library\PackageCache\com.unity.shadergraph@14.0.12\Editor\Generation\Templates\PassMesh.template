Pass
{
    $splice(PassName)
    Tags
    {
        $splice(LightMode)
    }

    // Render State
    $splice(RenderState)

    // Debug
    $splice(Debug)

    // --------------------------------------------------
    // Pass

    HLSLPROGRAM

    // Pragmas
    $splice(PassPragmas)

    // Keywords
    $splice(PassKeywords)
    $splice(GraphKeywords)

    // Defines
    $SurfaceType.Transparent:               #define _SURFACE_TYPE_TRANSPARENT 1
    $AlphaClip:                             #define _AlphaClip 1
    $Normal:                                #define _NORMALMAP 1
    $BlendMode.Add:                         #define _BLENDMODE_ADD 1
    $BlendMode.Premultiply:                 #define _ALPHAPREMULTIPLY_ON 1
    $NormalDropOffTS:                       #define _NORMAL_DROPOFF_TS 1
    $NormalDropOffOS:                       #define _NORMAL_DROPOFF_OS 1
    $NormalDropOffWS:                       #define _NORMAL_DROPOFF_WS 1
    $Attributes.normalOS:                   #define ATTRIBUTES_NEED_NORMAL
    $Attributes.tangentOS:                  #define ATTRIBUTES_NEED_TANGENT
    $Attributes.uv0:                        #define ATTRIBUTES_NEED_TEXCOORD0
    $Attributes.uv1:                        #define ATTRIBUTES_NEED_TEXCOORD1
    $Attributes.uv2:                        #define ATTRIBUTES_NEED_TEXCOORD2
    $Attributes.uv3:                        #define ATTRIBUTES_NEED_TEXCOORD3
    $Attributes.color:                      #define ATTRIBUTES_NEED_COLOR
    $Attributes.vertexID:                   #define ATTRIBUTES_NEED_VERTEXID
    $Varyings.positionWS:                   #define VARYINGS_NEED_POSITION_WS
    $Varyings.positionPredisplacementWS:    #define VARYINGS_NEED_POSITIONPREDISPLACEMENT_WS
    $Varyings.normalWS:                     #define VARYINGS_NEED_NORMAL_WS
    $Varyings.tangentWS:                    #define VARYINGS_NEED_TANGENT_WS
    $Varyings.texCoord0:                    #define VARYINGS_NEED_TEXCOORD0
    $Varyings.texCoord1:                    #define VARYINGS_NEED_TEXCOORD1
    $Varyings.texCoord2:                    #define VARYINGS_NEED_TEXCOORD2
    $Varyings.texCoord3:                    #define VARYINGS_NEED_TEXCOORD3
    $Varyings.color:                        #define VARYINGS_NEED_COLOR
    $Varyings.vertexID:                     #define VARYINGS_NEED_VERTEXID
    $Varyings.bitangentWS:                  #define VARYINGS_NEED_BITANGENT_WS
    $Varyings.screenPosition:               #define VARYINGS_NEED_SCREENPOSITION
    $Varyings.fogFactorAndVertexLight:      #define VARYINGS_NEED_FOG_AND_VERTEX_LIGHT
    $Varyings.cullFace:                     #define VARYINGS_NEED_CULLFACE
    $features.graphVertex:                  #define FEATURES_GRAPH_VERTEX
    $features.graphColorInterp:             #define FEATURES_GRAPH_COLOR_INTERP
    $Universal.UseLegacySpriteBlocks:       #define UNIVERSAL_USELEGACYSPRITEBLOCKS
    $splice(PassInstancing)
    $splice(GraphDefines)

    // Includes
    $splice(CustomInterpolatorPreInclude)

    $splice(PreGraphIncludes)

    // --------------------------------------------------
    // Structs and Packing

    $splice(CustomInterpolatorPrePacking)

    $splice(PassStructs)

    $splice(InterpolatorPack)

    // --------------------------------------------------
    // Graph

    // Graph Properties
    $splice(GraphProperties)

    // Graph Includes
    $splice(GraphIncludes)

    // -- Property used by ScenePickingPass
    #ifdef SCENEPICKINGPASS
    float4 _SelectionID;
    #endif

    // -- Properties used by SceneSelectionPass
    #ifdef SCENESELECTIONPASS
    int _ObjectId;
    int _PassValue;
    #endif

    // Graph Functions
    $splice(GraphFunctions)

    $splice(CustomInterpolatorPreVertex)

    // Graph Vertex
    $splice(GraphVertex)

    $splice(CustomInterpolatorPreSurface)

    // Graph Pixel
    $splice(GraphPixel)

    // --------------------------------------------------
    // Build Graph Inputs

    $features.graphVertex:  $include("BuildVertexDescriptionInputs.template.hlsl")
    $features.graphPixel:   $include("BuildSurfaceDescriptionInputs.template.hlsl")

    // --------------------------------------------------
    // Main

    $splice(PostGraphIncludes)

    ENDHLSL
}
