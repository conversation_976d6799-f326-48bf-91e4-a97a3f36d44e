{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 41876, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 41876, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 41876, "tid": 1115, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 41876, "tid": 1115, "ts": 1749198274891714, "dur": 373, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 41876, "tid": 1115, "ts": 1749198274894069, "dur": 723, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 41876, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 41876, "tid": 1, "ts": 1749198274653833, "dur": 3548, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41876, "tid": 1, "ts": 1749198274657385, "dur": 39112, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41876, "tid": 1, "ts": 1749198274696515, "dur": 41584, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 41876, "tid": 1115, "ts": 1749198274894797, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 41876, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274652525, "dur": 7255, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274659782, "dur": 225775, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274660320, "dur": 2279, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274662606, "dur": 1245, "ph": "X", "name": "ProcessMessages 14050", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274663857, "dur": 211, "ph": "X", "name": "ReadAsync 14050", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664070, "dur": 14, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664086, "dur": 30, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664118, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664121, "dur": 28, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664154, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664158, "dur": 16, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664177, "dur": 23, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664205, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664209, "dur": 53, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664266, "dur": 2, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664270, "dur": 25, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664297, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664299, "dur": 22, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664324, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664326, "dur": 21, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664349, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664351, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664371, "dur": 13, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664385, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664387, "dur": 67, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664458, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664460, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664496, "dur": 2, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664500, "dur": 28, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664530, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664533, "dur": 15, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664550, "dur": 63, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664617, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664652, "dur": 3, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664657, "dur": 25, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664687, "dur": 2, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664691, "dur": 19, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664711, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664713, "dur": 14, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664729, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664746, "dur": 22, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664772, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664775, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664795, "dur": 1, "ph": "X", "name": "ProcessMessages 1272", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664798, "dur": 22, "ph": "X", "name": "ReadAsync 1272", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664823, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664826, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664856, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664859, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664882, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664884, "dur": 20, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664907, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664909, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664933, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664935, "dur": 15, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664952, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664955, "dur": 14, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664971, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664973, "dur": 11, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274664987, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665015, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665030, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665032, "dur": 12, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665047, "dur": 12, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665062, "dur": 12, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665077, "dur": 15, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665095, "dur": 14, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665112, "dur": 12, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665126, "dur": 13, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665143, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665147, "dur": 11, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665161, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665185, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665188, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665207, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665209, "dur": 12, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665224, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665239, "dur": 17, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665259, "dur": 12, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665274, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665277, "dur": 19, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665298, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665315, "dur": 12, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665330, "dur": 12, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665345, "dur": 11, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665359, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665371, "dur": 12, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665386, "dur": 12, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665401, "dur": 12, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665416, "dur": 11, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665430, "dur": 12, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665445, "dur": 11, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665458, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665482, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665500, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665502, "dur": 22, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665529, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665532, "dur": 24, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665557, "dur": 1, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665559, "dur": 10, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665572, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665590, "dur": 18, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665610, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665613, "dur": 14, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665629, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665631, "dur": 16, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665648, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665650, "dur": 13, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665667, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665670, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665692, "dur": 18, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665712, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665714, "dur": 14, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665729, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665731, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665748, "dur": 12, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665764, "dur": 10, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665776, "dur": 20, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665798, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665813, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665815, "dur": 11, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665829, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665846, "dur": 12, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665861, "dur": 11, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665874, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665876, "dur": 33, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665913, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665915, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665940, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665943, "dur": 14, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665958, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665960, "dur": 13, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665976, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665997, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274665999, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666017, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666019, "dur": 16, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666037, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666039, "dur": 10, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666051, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666069, "dur": 10, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666081, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666095, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666112, "dur": 15, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666128, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666130, "dur": 12, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666147, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666150, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666171, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666193, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666197, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666215, "dur": 12, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666232, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666235, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666258, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666260, "dur": 18, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666280, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666282, "dur": 11, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666295, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666320, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666337, "dur": 10, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666350, "dur": 122, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666475, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666492, "dur": 11, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666506, "dur": 13, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666520, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666521, "dur": 11, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666535, "dur": 11, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666551, "dur": 10, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666564, "dur": 10, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666576, "dur": 12, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666589, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666591, "dur": 14, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666608, "dur": 11, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666622, "dur": 11, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666635, "dur": 12, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666650, "dur": 10, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666664, "dur": 11, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666678, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666700, "dur": 13, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666714, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666715, "dur": 11, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666729, "dur": 12, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666744, "dur": 10, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666757, "dur": 11, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666771, "dur": 11, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666785, "dur": 12, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666800, "dur": 10, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666812, "dur": 12, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666827, "dur": 12, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666841, "dur": 10, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666855, "dur": 10, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666866, "dur": 11, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666880, "dur": 12, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666895, "dur": 11, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666907, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666909, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666926, "dur": 13, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666941, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666943, "dur": 26, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666970, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274666988, "dur": 11, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667002, "dur": 11, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667015, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667016, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667036, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667050, "dur": 11, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667065, "dur": 9, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667077, "dur": 8, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667087, "dur": 12, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667102, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667123, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667127, "dur": 27, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667156, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667158, "dur": 13, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667173, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667198, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667218, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667220, "dur": 11, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667234, "dur": 13, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667248, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667250, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667272, "dur": 10, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667285, "dur": 12, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667299, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667313, "dur": 11, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667327, "dur": 12, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667342, "dur": 11, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667354, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667356, "dur": 11, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667369, "dur": 13, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667388, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667402, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667404, "dur": 12, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667419, "dur": 10, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667430, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667432, "dur": 12, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667446, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667463, "dur": 10, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667475, "dur": 10, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667488, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667503, "dur": 12, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667518, "dur": 11, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667532, "dur": 11, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667546, "dur": 11, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667560, "dur": 9, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667572, "dur": 11, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667586, "dur": 15, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667604, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667619, "dur": 11, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667632, "dur": 11, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667645, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667646, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667660, "dur": 10, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667673, "dur": 10, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667685, "dur": 11, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667700, "dur": 11, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667712, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667713, "dur": 13, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667729, "dur": 12, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667744, "dur": 11, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667757, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667772, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667786, "dur": 14, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667806, "dur": 15, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667824, "dur": 13, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667840, "dur": 11, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667853, "dur": 9, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667865, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667879, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667896, "dur": 11, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667910, "dur": 11, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667924, "dur": 12, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667938, "dur": 11, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667952, "dur": 10, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667964, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667978, "dur": 11, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274667992, "dur": 11, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668006, "dur": 11, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668020, "dur": 13, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668035, "dur": 10, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668048, "dur": 11, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668061, "dur": 11, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668075, "dur": 11, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668087, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668089, "dur": 13, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668104, "dur": 10, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668117, "dur": 16, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668137, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668156, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668158, "dur": 14, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668174, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668192, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668194, "dur": 13, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668209, "dur": 11, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668223, "dur": 11, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668237, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668250, "dur": 11, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668264, "dur": 11, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668278, "dur": 10, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668291, "dur": 13, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668307, "dur": 12, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668322, "dur": 9, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668334, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668347, "dur": 11, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668361, "dur": 11, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668373, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668375, "dur": 11, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668387, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668389, "dur": 12, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668403, "dur": 10, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668415, "dur": 9, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668427, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668441, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668459, "dur": 12, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668473, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668474, "dur": 13, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668490, "dur": 13, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668506, "dur": 10, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668518, "dur": 10, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668530, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668548, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668549, "dur": 13, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668565, "dur": 12, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668579, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668593, "dur": 11, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668607, "dur": 9, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668619, "dur": 11, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668633, "dur": 13, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668649, "dur": 9, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668661, "dur": 71, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668736, "dur": 18, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668756, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668777, "dur": 35, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668816, "dur": 4, "ph": "X", "name": "ProcessMessages 2883", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668822, "dur": 26, "ph": "X", "name": "ReadAsync 2883", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668850, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668853, "dur": 14, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668868, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668870, "dur": 13, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668885, "dur": 11, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668900, "dur": 13, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668915, "dur": 11, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668929, "dur": 13, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668945, "dur": 14, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668962, "dur": 17, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668982, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274668998, "dur": 11, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669012, "dur": 11, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669026, "dur": 11, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669039, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669064, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669066, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669083, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669085, "dur": 26, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669114, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669144, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669146, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669163, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669165, "dur": 52, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669220, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669236, "dur": 10, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669248, "dur": 12, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669264, "dur": 52, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669318, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669333, "dur": 12, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669346, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669348, "dur": 11, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669362, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669410, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669429, "dur": 11, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669443, "dur": 12, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669456, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669458, "dur": 73, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669533, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669547, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669564, "dur": 12, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669577, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669578, "dur": 56, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669637, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669653, "dur": 11, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669667, "dur": 11, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669681, "dur": 57, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669740, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669755, "dur": 12, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669769, "dur": 11, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669783, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669834, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669850, "dur": 11, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669864, "dur": 9, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669876, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669901, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669915, "dur": 51, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669968, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274669986, "dur": 12, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670001, "dur": 11, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670015, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670066, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670079, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670081, "dur": 14, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670098, "dur": 12, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670111, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670113, "dur": 59, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670176, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670179, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670212, "dur": 4, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670219, "dur": 41, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670272, "dur": 5, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670280, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670340, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670343, "dur": 25, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670372, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670376, "dur": 37, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670416, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670434, "dur": 11, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670448, "dur": 12, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670461, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670463, "dur": 52, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670517, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670532, "dur": 11, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670547, "dur": 16, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670568, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670572, "dur": 48, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670623, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670648, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670651, "dur": 25, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670681, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670686, "dur": 42, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670731, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670746, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670748, "dur": 11, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670762, "dur": 11, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670776, "dur": 50, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670828, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670844, "dur": 12, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670859, "dur": 12, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670872, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670874, "dur": 60, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670936, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670951, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670966, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670968, "dur": 11, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274670983, "dur": 50, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671036, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671051, "dur": 13, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671067, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671081, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671100, "dur": 10, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671112, "dur": 50, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671165, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671182, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671183, "dur": 13, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671197, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671199, "dur": 15, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671217, "dur": 11, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671231, "dur": 12, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671244, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671246, "dur": 11, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671260, "dur": 10, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671273, "dur": 11, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671287, "dur": 9, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671298, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671348, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671362, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671376, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671378, "dur": 11, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671392, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671439, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671458, "dur": 9, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671469, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671485, "dur": 11, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671499, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671549, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671565, "dur": 11, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671579, "dur": 11, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671592, "dur": 51, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671646, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671660, "dur": 12, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671674, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671676, "dur": 11, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671688, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671690, "dur": 12, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671706, "dur": 11, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671718, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671720, "dur": 10, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671733, "dur": 11, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671747, "dur": 10, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671759, "dur": 10, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671772, "dur": 47, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671821, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671836, "dur": 12, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671851, "dur": 11, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671863, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671865, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671914, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671928, "dur": 12, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671943, "dur": 11, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671956, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274671958, "dur": 47, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672008, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672023, "dur": 13, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672038, "dur": 10, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672052, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672101, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672114, "dur": 17, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672134, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672137, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672164, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672168, "dur": 40, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672211, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672226, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672228, "dur": 12, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672243, "dur": 11, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672257, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672313, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672328, "dur": 12, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672343, "dur": 11, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672356, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672406, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672421, "dur": 11, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672434, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672436, "dur": 14, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672452, "dur": 10, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672465, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672479, "dur": 14, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672495, "dur": 11, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672508, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672509, "dur": 11, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672523, "dur": 14, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672540, "dur": 49, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672592, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672606, "dur": 13, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672622, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672639, "dur": 11, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672653, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672666, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672668, "dur": 11, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672681, "dur": 10, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672695, "dur": 17, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672714, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672729, "dur": 50, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672781, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672797, "dur": 13, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672812, "dur": 11, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672826, "dur": 9, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672838, "dur": 43, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672883, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672899, "dur": 12, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672914, "dur": 13, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672930, "dur": 9, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672941, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274672988, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673003, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673020, "dur": 11, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673032, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673034, "dur": 59, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673099, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673113, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673115, "dur": 11, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673130, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673148, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673150, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673193, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673211, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673230, "dur": 10, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673243, "dur": 53, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673299, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673313, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673329, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673331, "dur": 13, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673347, "dur": 11, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673361, "dur": 12, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673375, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673377, "dur": 11, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673390, "dur": 11, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673404, "dur": 12, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673419, "dur": 9, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673431, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673478, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673494, "dur": 12, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673509, "dur": 11, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673521, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673523, "dur": 52, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673578, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673592, "dur": 11, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673607, "dur": 11, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673621, "dur": 49, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673672, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673687, "dur": 13, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673701, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673704, "dur": 11, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673717, "dur": 47, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673766, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673780, "dur": 11, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673794, "dur": 11, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673808, "dur": 12, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673823, "dur": 12, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673837, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673839, "dur": 12, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673853, "dur": 11, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673867, "dur": 9, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673878, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673892, "dur": 49, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673944, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673959, "dur": 12, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673975, "dur": 11, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274673989, "dur": 54, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674046, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674064, "dur": 11, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674078, "dur": 11, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674092, "dur": 14, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674107, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674109, "dur": 11, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674123, "dur": 24, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674148, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674150, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674169, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674172, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674190, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674248, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674262, "dur": 95, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674365, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674370, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674417, "dur": 391, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674816, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674883, "dur": 7, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674893, "dur": 62, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674964, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274674972, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675007, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675011, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675044, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675074, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675079, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675108, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675137, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675142, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675171, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675175, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675239, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675243, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675280, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675284, "dur": 170, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675463, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675519, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675525, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675570, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675575, "dur": 45, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675624, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675630, "dur": 44, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675679, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675685, "dur": 41, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675732, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675737, "dur": 39, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675778, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675781, "dur": 39, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675822, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675825, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675860, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675865, "dur": 40, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675910, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675914, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675963, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274675968, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676007, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676014, "dur": 43, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676062, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676068, "dur": 47, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676120, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676125, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676176, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676181, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676215, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676218, "dur": 41, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676265, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676270, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676319, "dur": 4, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676324, "dur": 39, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676368, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676374, "dur": 50, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676430, "dur": 3, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676436, "dur": 47, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676486, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676491, "dur": 44, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676541, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676548, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676587, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676592, "dur": 24, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676621, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676625, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676646, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274676649, "dur": 7833, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684488, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684492, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684545, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684550, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684594, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684599, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684630, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684658, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274684662, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685054, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685092, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685096, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685136, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685139, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685174, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685218, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685220, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685240, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685489, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685524, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685529, "dur": 214, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685748, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685751, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685788, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685790, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685820, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685824, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685857, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685861, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685895, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685897, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685931, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685934, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685975, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274685978, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686004, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686045, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686075, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686077, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686136, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686141, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686183, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686187, "dur": 373, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686565, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686568, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686607, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686611, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686795, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686820, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686824, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686856, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686860, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686894, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686896, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686933, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686938, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686961, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686964, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686997, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274686999, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687033, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687037, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687059, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687075, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687091, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687109, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687127, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687129, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687156, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687159, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687192, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687195, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687232, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687267, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687272, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687309, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687311, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687333, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687335, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687346, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687457, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687486, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687488, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687513, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687515, "dur": 208, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687729, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687761, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687765, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687795, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687798, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687900, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687917, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687919, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687938, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687941, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687956, "dur": 11, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687970, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687995, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274687999, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688041, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688067, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688071, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688106, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688110, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688146, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688151, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688173, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688227, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688240, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688298, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688330, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688368, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688372, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688409, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688446, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688474, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688476, "dur": 308, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688791, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688813, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688815, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688857, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688861, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688878, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688964, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688991, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274688993, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689040, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689071, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689157, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689187, "dur": 589, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689781, "dur": 46, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689832, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689837, "dur": 33, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689877, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274689894, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690138, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690156, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690169, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690182, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690200, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690204, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690230, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690245, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690266, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690281, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690297, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690489, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690507, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690521, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690574, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274690588, "dur": 480, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274691072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274691075, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274691090, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274691094, "dur": 42240, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274733345, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274733351, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274733376, "dur": 1965, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274735347, "dur": 9552, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274744910, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274744918, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274744977, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274744983, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745037, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745042, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745163, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745196, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745199, "dur": 570, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745775, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745779, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745828, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745833, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274745879, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746007, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746011, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746058, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746061, "dur": 268, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746334, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746337, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746369, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746372, "dur": 403, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746781, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746814, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746819, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746855, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274746859, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747016, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747065, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747070, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747129, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747134, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747171, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747174, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747218, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747222, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747268, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747273, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747301, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747306, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747336, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747340, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747395, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747400, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747450, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747454, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747492, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747495, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747534, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747538, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747578, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747583, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747652, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747655, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747699, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747703, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747746, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747750, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747784, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747789, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747832, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747836, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747868, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747870, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747926, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747929, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747963, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274747968, "dur": 309, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748281, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748285, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748330, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748334, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748376, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748379, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748407, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274748409, "dur": 899, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749317, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749337, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749342, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749408, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749452, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749456, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749487, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749520, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749557, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749562, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749597, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749600, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749695, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749736, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749738, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749793, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749795, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749828, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749831, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749874, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749878, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749908, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749911, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749951, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749979, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274749981, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750005, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750025, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750028, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750092, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750110, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750112, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750123, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750125, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750168, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750193, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750195, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750218, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750220, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750245, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750247, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750279, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750297, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750312, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274750327, "dur": 43370, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274793704, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274793708, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274793748, "dur": 3765, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274797521, "dur": 4014, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274801542, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274801546, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274801565, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274801568, "dur": 48859, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274850436, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274850441, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274850464, "dur": 27, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274850492, "dur": 23232, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274873731, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274873738, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274873761, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274873765, "dur": 1489, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875264, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875268, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875333, "dur": 43, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875379, "dur": 166, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875551, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875554, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875603, "dur": 33, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274875637, "dur": 839, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274876482, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274876486, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274876564, "dur": 607, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 41876, "tid": 12884901888, "ts": 1749198274877179, "dur": 8319, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 41876, "tid": 1115, "ts": 1749198274894819, "dur": 2424, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 41876, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 41876, "tid": 8589934592, "ts": 1749198274650733, "dur": 87391, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 41876, "tid": 8589934592, "ts": 1749198274738127, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 41876, "tid": 8589934592, "ts": 1749198274738135, "dur": 826, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 41876, "tid": 1115, "ts": 1749198274897245, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 41876, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 41876, "tid": 4294967296, "ts": 1749198274637931, "dur": 248424, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 41876, "tid": 4294967296, "ts": 1749198274640728, "dur": 5796, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 41876, "tid": 4294967296, "ts": 1749198274886370, "dur": 3161, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 41876, "tid": 4294967296, "ts": 1749198274887911, "dur": 104, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 41876, "tid": 4294967296, "ts": 1749198274889610, "dur": 135, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 41876, "tid": 1115, "ts": 1749198274897257, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749198274657707, "dur": 1481, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274659197, "dur": 515, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274659840, "dur": 679, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274661103, "dur": 221, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1A296E8E6EB4A3C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749198274661925, "dur": 1668, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749198274660533, "dur": 13237, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274673780, "dur": 201348, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274875180, "dur": 57, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274875237, "dur": 312, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274875553, "dur": 170, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274875980, "dur": 1491, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749198274660243, "dur": 13552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749198274673845, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274673927, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749198274673839, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D0A9626203618652.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274674203, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274674200, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_9A321D807FE780A5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274674622, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274674620, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_089E5D39F5D50B51.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274674995, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749198274675064, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_089E5D39F5D50B51.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274675374, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274675791, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274675844, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274675929, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274676015, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274676299, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274676449, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274676516, "dur": 391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274676908, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677051, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677141, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677421, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677492, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677572, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677788, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274677924, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678064, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678125, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678204, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678333, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678556, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678769, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274678901, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679099, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679213, "dur": 348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679589, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679688, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679745, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679837, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679908, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274679978, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680053, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680111, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680169, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680259, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680346, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680426, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680505, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680571, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680675, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680814, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680872, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274680992, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681121, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681180, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681279, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681365, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681467, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681564, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681656, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681734, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681789, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681877, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274681941, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682023, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682143, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682228, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682361, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682540, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682666, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682801, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682891, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274682969, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683048, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683126, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683203, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683279, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683376, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683458, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683515, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683575, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683648, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274683716, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274675491, "dur": 8491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274684078, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274684147, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274684649, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274684729, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274685052, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274685377, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749198274686037, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274685447, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274686652, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274686826, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274687542, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274687684, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274688734, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274688816, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274689260, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274689337, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274689701, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274689780, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274690021, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749198274690091, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274690595, "dur": 102575, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274798567, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274798566, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274800830, "dur": 175, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749198274801041, "dur": 48879, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749198274873083, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274873082, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274873215, "dur": 1537, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749198274874755, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274660236, "dur": 13551, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274673860, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274673849, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_189198D392E53F70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674027, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674025, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_2443A696A5D2F1CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674366, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_2443A696A5D2F1CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674424, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674423, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_31B76E1CF8ED0671.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674556, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674555, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_10E763D36CD3FEFA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749198274674670, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274675060, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749198274675213, "dur": 916, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749198274676130, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274677363, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274677777, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274678107, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274678402, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274678788, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274679525, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274680150, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274680706, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274681344, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274681946, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274682352, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274683816, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274684105, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274684632, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274685090, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274686336, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274686549, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274686654, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274686800, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749198274687008, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749198274687612, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274687873, "dur": 53133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274741709, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274743199, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274743630, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274744726, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274744805, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749198274741007, "dur": 5089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749198274746097, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749198274746507, "dur": 3259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749198274749809, "dur": 125371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274660346, "dur": 13466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274673819, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274673875, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749198274673815, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B95F7351B49C483F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749198274674049, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274674047, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_F3FC38EE2EE72536.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749198274674284, "dur": 748, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274674282, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_DC693B31A43EDF8D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749198274675039, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274675135, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_DC693B31A43EDF8D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749198274675348, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749198274675450, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749198274675837, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13279423640760612673.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749198274676031, "dur": 3084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274679116, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274679980, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274680827, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274681544, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274682300, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274682732, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274684098, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274684623, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274685080, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749198274685284, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749198274685932, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274686098, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274686345, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274686533, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274686668, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274686836, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274687542, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274687881, "dur": 56174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274744290, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274744885, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274745816, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274746489, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274746586, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274747074, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749198274744057, "dur": 5070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749198274749128, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749198274749203, "dur": 125936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274660287, "dur": 13513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274673815, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749198274673804, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_DC561A479FC70E8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749198274674015, "dur": 431, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749198274674013, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_B4D7E26C3DF1D3DF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749198274674463, "dur": 566, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749198274674462, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_CC0FA3C25264E377.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675117, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675320, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675401, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675514, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675653, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675844, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5068094731376506261.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749198274675997, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274676970, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274677273, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274677564, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274677909, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274678244, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274678623, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274679119, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274679934, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274680482, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274681073, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274681533, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274681975, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274682584, "dur": 1497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274684081, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274684610, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274685060, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749198274685549, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749198274686252, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274686355, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274686544, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274686677, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274686841, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274687545, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274687887, "dur": 56165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749198274744224, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749198274744805, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749198274745454, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Routing.Abstractions.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 4, "ts": ****************, "dur": 4822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749198274748938, "dur": 126191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274660315, "dur": 13492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274673817, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274673876, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1749198274673809, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_DCB45F0869494A31.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274673999, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274673997, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_D8BD2B48BB0E9ACA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674128, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674127, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A9C3CF9AA3C8B7F0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674409, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674408, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5C0B462DB8A77E6E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674553, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674552, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_C485A298ECB4A11E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674718, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274674717, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675000, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274675059, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675134, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675259, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675455, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675590, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675660, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675743, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749198274675896, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14706505386486315648.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749198274676057, "dur": 2907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274678965, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274679809, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274680379, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274681148, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274681809, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274682473, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274682564, "dur": 1503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274684067, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274684602, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274685055, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274685410, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274685468, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749198274686042, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274686448, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274686528, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274686653, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749198274686817, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749198274687382, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274687491, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274687548, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274687891, "dur": 53162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274743253, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274744989, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274745158, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.1\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274741057, "dur": 4203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749198274745261, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749198274747330, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clretwrc.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749198274745352, "dur": 3957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749198274749366, "dur": 125780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274660805, "dur": 13153, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274673969, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274673960, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_A0375A3CF61936AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274674300, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_A0375A3CF61936AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274674412, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274674410, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_2963B9D870E68354.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274674715, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274674713, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274675103, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749198274675247, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749198274675503, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1749198274675612, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749198274675850, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14811776502145285846.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749198274675973, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1191125675877671024.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749198274676047, "dur": 2954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274679002, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274679899, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274680483, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274681094, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274681682, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274682191, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274683895, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274684071, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274684601, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274685051, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274685436, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274685492, "dur": 1797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749198274687289, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274687498, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274687622, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749198274688561, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749198274688656, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749198274689012, "dur": 52012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274742270, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274744286, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274744619, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274745307, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 6, "ts": ****************, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274746470, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749198274741025, "dur": 6140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749198274747165, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274747256, "dur": 2223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749198274749500, "dur": 125653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274660380, "dur": 13445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274673840, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274673829, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_00175D1AD326F984.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749198274674017, "dur": 374, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274674016, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_4CF5139186B78D34.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749198274674413, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274674411, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_BDD51A86FEC2736C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749198274674711, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274675063, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1749198274675281, "dur": 796, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749198274676078, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274676597, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274676948, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274677367, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274677777, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274678108, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274678376, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274678962, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274679879, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274680498, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274681230, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274681956, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274682387, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274682589, "dur": 1492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274684081, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274684607, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274685072, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749198274685281, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749198274686246, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274686353, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274686416, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749198274686647, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749198274687257, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274687546, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274687900, "dur": 53113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274744225, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274744688, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274741016, "dur": 4168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749198274745186, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749198274745308, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274746225, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274746393, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274746646, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274746704, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749198274745269, "dur": 4166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749198274749507, "dur": 125655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274660844, "dur": 13128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274673992, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274673977, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_267CBA5B1D031FE0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749198274674268, "dur": 718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274674266, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8423300B94E0B3F8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675023, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675214, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675454, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675660, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675777, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675848, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4082344215324493762.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749198274675952, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4082344215324493762.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749198274676114, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274676555, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274677687, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274678024, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274678379, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274678870, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274679716, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274680196, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274680768, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274681287, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274682070, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274683708, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274684107, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274684633, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274685101, "dur": 1250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274686352, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274686540, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274686664, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274686827, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274687551, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274687895, "dur": 53113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274744049, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274744451, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274744590, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274744686, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274745355, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274745991, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749198274741032, "dur": 5480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749198274746513, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274746777, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274746855, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274746924, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274746979, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274747794, "dur": 1018, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274748819, "dur": 124265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749198274873087, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749198274873086, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749198274873225, "dur": 1825, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749198274660858, "dur": 13122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274674005, "dur": 573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274673985, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E353DE2F82A4FBDC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749198274674609, "dur": 437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274674607, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17A6599B84A9C4C8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749198274675108, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17A6599B84A9C4C8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749198274675358, "dur": 365, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749198274675816, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749198274675904, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7480027478895629309.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749198274676071, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274676591, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274676997, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274677357, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274677720, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274678507, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274678865, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274679737, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274680321, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274680910, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274681428, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274682337, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274682691, "dur": 1398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274684090, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274684625, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274685085, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274686331, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274686522, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274686659, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274686818, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274687552, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274687897, "dur": 53114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274744224, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274744687, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274745061, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274745307, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274746037, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749198274741012, "dur": 5452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749198274746465, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274746586, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274746672, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274746939, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274747198, "dur": 1992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749198274749218, "dur": 125922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274660873, "dur": 13116, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274674007, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274673993, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A97A679C7316D60C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274674391, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274674390, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1A9C03D8D2995E09.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274674480, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274674479, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1A296E8E6EB4A3C9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274674708, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.1\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274674707, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_977425616863A8B8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274675020, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_977425616863A8B8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274675279, "dur": 506, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749198274675786, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749198274675911, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749198274676461, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274677237, "dur": 1125, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Formats.Asn1.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274676020, "dur": 4102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274680123, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274680784, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274681323, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274681864, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274682441, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274682563, "dur": 1500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274684126, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274684606, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274685064, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274685278, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749198274686416, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274686562, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749198274686670, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749198274687817, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274687869, "dur": 53172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749198274744496, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274744882, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274744986, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274745487, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274741043, "dur": 4746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749198274746224, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274746694, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274747048, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749198274745833, "dur": 3749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749198274749621, "dur": 125549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274660432, "dur": 13444, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274673893, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274673881, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A0CD99413D3BEBEA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749198274674077, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274674075, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_5A0A4E1105BFB91C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749198274674271, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274674269, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_069C6330A3B0DB5F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749198274674756, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274675017, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749198274675316, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749198274675503, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749198274675639, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749198274675786, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749198274675906, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6475187423424434156.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749198274676007, "dur": 2792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274678800, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274679234, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274680168, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274680710, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274681346, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274682117, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274683790, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274684103, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274684633, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274685094, "dur": 1249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274686343, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274686531, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274686653, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274686799, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749198274687002, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749198274687545, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274687638, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749198274687738, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749198274688379, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749198274688476, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749198274688894, "dur": 52156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274744047, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274744114, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274744291, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274744390, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274745063, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274745430, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274746037, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274746410, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274746522, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274746585, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274746810, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274747049, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749198274741054, "dur": 6072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749198274747127, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274747247, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274747425, "dur": 2253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749198274749698, "dur": 125466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274660496, "dur": 13389, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274673894, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274673888, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_428354B704A21A10.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749198274674045, "dur": 577, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274674043, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C46FDC424E186CE8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749198274674655, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274674653, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_B6277882EE31D3F7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749198274675042, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274675119, "dur": 526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749198274675646, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749198274675750, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749198274675861, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5748731363401015749.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749198274676024, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3309625357498479658.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749198274676122, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274676571, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274677019, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274677313, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274677927, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274678207, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274678554, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274678987, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274679661, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274680092, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274680686, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274681195, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274681871, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274682311, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274682768, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274684092, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274684627, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274685097, "dur": 1247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274686344, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274686542, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274686681, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274686837, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274687542, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274687885, "dur": 53125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274742395, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274744358, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274744804, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274745205, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274745493, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274741011, "dur": 5206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749198274746218, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749198274746647, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274746842, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749198274746287, "dur": 3402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749198274749733, "dur": 125443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274660529, "dur": 13365, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274673906, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274673898, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_2F46F8E212FD0109.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749198274674094, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274674092, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5999F58F3F64BE4D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749198274674644, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274674643, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F345AFED83A4395B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749198274675020, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274675071, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F345AFED83A4395B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749198274675223, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749198274675318, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749198274675552, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749198274675783, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749198274675964, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2123039382149407573.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749198274676064, "dur": 2144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274678209, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274678620, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274678983, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274679834, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274680649, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274681292, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274681797, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274682512, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274682616, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274684090, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274684617, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274685077, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749198274685498, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749198274686388, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274686557, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274686658, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274686819, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274687539, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274687866, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274688382, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749198274688478, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749198274688846, "dur": 52174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274744071, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Features.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274744769, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274744940, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Memory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274745645, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274746673, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749198274741021, "dur": 6496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.SmoothNormalTool.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749198274747518, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274747891, "dur": 1907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749198274749830, "dur": 125305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274660567, "dur": 13335, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274673917, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274673907, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_2E8852F685A32A98.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749198274674089, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274674087, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_86245EDCA4DC0DB8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749198274674374, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_86245EDCA4DC0DB8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749198274674643, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274674640, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_3A06B418DEE93D07.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675028, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274675080, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_3A06B418DEE93D07.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675214, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675314, "dur": 319, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675633, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675748, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675884, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16186684979044175686.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749198274675963, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16186684979044175686.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749198274676056, "dur": 3601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274679657, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274680262, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274680893, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274681399, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274681897, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274682345, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274683640, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274684101, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274684630, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274685093, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274686337, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274686543, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274686664, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274686830, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274687541, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274687883, "dur": 53614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274744619, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274744940, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274745328, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274745547, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274746393, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274746631, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274746695, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274746829, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749198274741498, "dur": 5554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749198274747053, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274747201, "dur": 2103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749198274749328, "dur": 125814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274660594, "dur": 13316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274673923, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274673914, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_C379C3A289970A71.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749198274674117, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274674115, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_283B065E6FAE6C13.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749198274674476, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274674475, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EC134644BEAE1306.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749198274674662, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274674721, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274674720, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_23C6CE254F102A40.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749198274675034, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_23C6CE254F102A40.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749198274675137, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749198274675460, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749198274675591, "dur": 478, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749198274676070, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274676499, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274677057, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274677421, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274677832, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274678120, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274678502, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274679030, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274679792, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274680559, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274681307, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274681888, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274682356, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274683332, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274684104, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274684631, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274685092, "dur": 1245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274686337, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274686536, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274686660, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274686821, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274687558, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274687903, "dur": 53149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274744941, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274741053, "dur": 4241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.SmoothNormalTool.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749198274745294, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749198274745547, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274745762, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274746521, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749198274745505, "dur": 3936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749198274749489, "dur": 125663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274660636, "dur": 13281, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274673928, "dur": 424, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274673920, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8CAE94E964334601.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274674366, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8CAE94E964334601.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274674430, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274674429, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5F80DFE48AFACF2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274674573, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274674572, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_63FDA75392468D7E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274674716, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274674995, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274675108, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274675275, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749198274675690, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749198274675948, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5147072719652273855.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749198274676034, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5147072719652273855.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749198274676136, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274676824, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274677163, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274677493, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274677846, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274678114, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274678484, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274678821, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274679539, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274680131, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274680607, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274681362, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274682123, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274683832, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274684107, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274684640, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274685059, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274685355, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274685543, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749198274686538, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274686688, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274686807, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274687538, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274687865, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749198274687962, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749198274688295, "dur": 52745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274744117, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274744288, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274744496, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274744687, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274745736, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749198274741040, "dur": 5558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749198274746598, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": ****************, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274746916, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274746996, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274747879, "dur": 1844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749198274749740, "dur": 125435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274660685, "dur": 13240, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274673936, "dur": 350, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274673929, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_3086D49D18FB50F6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749198274674311, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274674309, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_ED461FB813D35A26.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749198274674451, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274674450, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_3708CF866B4DD792.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749198274674713, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274675060, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749198274675141, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749198274675285, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749198274675844, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14944518940209746966.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749198274675912, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10756517099639384098.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749198274677424, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274676050, "dur": 3610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274679661, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274680241, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274680821, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274681370, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274682297, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274682720, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274684087, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274684614, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274685071, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749198274685300, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274685642, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749198274686347, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274686546, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274686669, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274686834, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274687547, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274687891, "dur": 53130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274744451, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274741040, "dur": 3566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749198274744607, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749198274746165, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274746645, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-stdio-l1-1-0.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274746796, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Core.Api.dll"}}, {"pid": 12345, "tid": 17, "ts": ****************, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostpolicy.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274748325, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749198274744670, "dur": 4324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749198274749039, "dur": 126086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274660711, "dur": 13218, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274673936, "dur": 361, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274673931, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F3FBE73F9E38DC0A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749198274674316, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274674314, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_C2F4ECECEA24D681.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749198274674487, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274674486, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_3C80C46DA37A4017.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749198274674775, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274675057, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749198274675234, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749198274675536, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1749198274675727, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1749198274675935, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7917304513136103991.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749198274676060, "dur": 3498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274679558, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274680362, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274680853, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274681650, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274682249, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274682878, "dur": 1224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274684102, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274684626, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274685088, "dur": 1250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274686338, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274686529, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274686672, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274686833, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274687543, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274687880, "dur": 53157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274741038, "dur": 2982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749198274744022, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749198274744621, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274744770, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274746842, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.ClientFactory.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274746924, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274748216, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749198274744467, "dur": 4898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749198274749412, "dur": 125735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274660731, "dur": 13206, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274673952, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274673941, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5B3C12E989D584E0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749198274674175, "dur": 434, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274674173, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5249E70EF063C787.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749198274674638, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274674637, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3978CED332DF606.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749198274675023, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274675182, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1749198274675763, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749198274675997, "dur": 2000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274677997, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274678324, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274678799, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274679179, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274679945, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274680485, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274681134, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274681739, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274682416, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274682566, "dur": 1513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274684079, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274684621, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274685083, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749198274685547, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749198274686069, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274686392, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274686550, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274686645, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274686802, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274687556, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274687904, "dur": 53112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274744359, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274745091, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274745545, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274746604, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.1\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749198274741024, "dur": 5718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749198274746743, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274746925, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274747058, "dur": 1858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749198274748948, "dur": 126189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274660765, "dur": 13180, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274673960, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274673949, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_3FE5E5FDEEDC094E.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674121, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674120, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_7F0BB9FB87BE218F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674474, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674473, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_360E9C643FF83260.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674571, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674571, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E5D0E1ABFD68B096.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674707, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274674705, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274675180, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1749198274675622, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749198274675722, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749198274675857, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9280965716228574833.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749198274675998, "dur": 3555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274679553, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274680223, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274680896, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274681539, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274682003, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274682590, "dur": 1493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274684083, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274684613, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274685067, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274685309, "dur": 1825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749198274687134, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274687281, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274687423, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749198274687867, "dur": 1396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274689264, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749198274689401, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749198274689804, "dur": 51251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749198274744077, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.TypeExtensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274744805, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274745355, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274745430, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274745492, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274745817, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749198274741056, "dur": 5234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749198274746330, "dur": 3314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749198274749690, "dur": 125476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274660790, "dur": 13163, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274673966, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1749198274673956, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_8BA40E5888843C47.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1749198274674396, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1749198274674395, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA1C9EB3BDD37A6C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1749198274674565, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1749198274674564, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_74C561A8BABE8A88.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1749198274674762, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274675065, "dur": 520, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1749198274675754, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1749198274675891, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17626248429526979731.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1749198274676023, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17278736735188021267.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1749198274676104, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274676600, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274677614, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274677996, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274678294, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274678635, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274678978, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274679723, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274680206, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274681002, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274681439, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274682010, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274682677, "dur": 1408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274684085, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274684619, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274685081, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1749198274685417, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1749198274686444, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274686566, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274686646, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274686801, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1749198274686959, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1749198274687480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274687548, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274687890, "dur": 53157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274744803, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 21, "ts": 1749198274746037, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 21, "ts": 1749198274741049, "dur": 5059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1749198274746108, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274746657, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274746836, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274746971, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274747344, "dur": 2138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1749198274749530, "dur": 125625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274660364, "dur": 13454, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274673830, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274673884, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1749198274673821, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E44F34F0175DE19.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674001, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674000, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_82191F67B4B78681.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674390, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674389, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B3B3A6DDF87C7F25.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674471, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674471, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A9682FBDF4F4AE75.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674725, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674702, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274674971, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274675176, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1749198274675550, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1749198274675831, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/599482131929635811.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1749198274676020, "dur": 2878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274678898, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274679612, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274680209, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274680906, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274681348, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274682026, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274682526, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274682577, "dur": 1497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274684075, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274684615, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274685078, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274685395, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274685948, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274686190, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274685451, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1749198274686524, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274686667, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274686832, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274687537, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274687868, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1749198274687951, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1749198274688298, "dur": 52740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274744049, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274744226, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274744854, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274746131, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 22, "ts": 1749198274741042, "dur": 5499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1749198274746790, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274747018, "dur": 859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274747877, "dur": 1809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1749198274749686, "dur": 125477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274660822, "dur": 13140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274673974, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274673966, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_47ADA40E4AECCCDF.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274674305, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274674304, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C01DD0424B7D6F9B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274674453, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274674452, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA6B1A5D11BE608C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274674693, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA6B1A5D11BE608C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274674790, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274675030, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1749198274675131, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1749198274675405, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1749198274675477, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1749198274675868, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12642482374746149726.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1749198274676013, "dur": 3066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274679079, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274680014, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274680755, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274681238, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274682022, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274682560, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274684065, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274684602, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274685091, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274685293, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1749198274686672, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274686800, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274686966, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1749198274687866, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274687953, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1749198274689097, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274689161, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1749198274689669, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1749198274689746, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1749198274690000, "dur": 51026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274743369, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Configuration.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274744588, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274744854, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274745092, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274745492, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274745760, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274746393, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 23, "ts": 1749198274741040, "dur": 5411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1749198274746456, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274746624, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274746927, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274747147, "dur": 1882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1749198274749077, "dur": 126046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274660392, "dur": 13440, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274673841, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274673926, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1749198274673834, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_11620CA68616F987.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1749198274674230, "dur": 764, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274674228, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AE22A2ABB21D3781.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1749198274675125, "dur": 454, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1749198274675634, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1749198274675736, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1749198274675898, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11793066456053230617.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1749198274677564, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Core.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274676048, "dur": 3734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274679783, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274680212, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274680798, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274681377, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274681949, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274682400, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274682462, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274682569, "dur": 1506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274684075, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274684612, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274685077, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1749198274685331, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274685384, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1749198274686195, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274686411, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274686547, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274686662, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274686824, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274687550, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274687894, "dur": 53152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274744225, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274744405, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Quic.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274744727, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.AccessControl.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274745208, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274745309, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274746542, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 24, "ts": 1749198274741054, "dur": 6179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1749198274747234, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274747365, "dur": 2248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1749198274749629, "dur": 125532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274660404, "dur": 13446, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274673868, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1749198274673855, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7FA7E3FD6F1DEC6E.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1749198274674041, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1749198274674039, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_0642E685C8E11829.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1749198274674253, "dur": 727, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1749198274674251, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41D4E76F025E00C5.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1749198274675003, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41D4E76F025E00C5.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1749198274675276, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1749198274675512, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1749198274675943, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4231056368997063253.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1749198274676026, "dur": 2833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274678859, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274679645, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274680458, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274681005, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274681504, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274682061, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274682897, "dur": 1196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274684093, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274684622, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274685084, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274686330, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274686523, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274686663, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274686824, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274687545, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274687889, "dur": 53143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274744049, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 25, "ts": 1749198274741036, "dur": 3300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1749198274744337, "dur": 2193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274746598, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274746866, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274746966, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274747232, "dur": 2121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1749198274749380, "dur": 125774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274660417, "dur": 13441, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274673872, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274673861, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9F13C49BEAE3DA8D.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274673996, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274673995, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_21AC31B013A95557.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674393, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674392, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_8638D8465538AE25.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674478, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674477, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_543A080B82168C72.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674723, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674704, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_4B1E8EC60049CCFD.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274674995, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274675085, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274675085, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_D8F49B6184FDE9B7.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274675157, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1749198274675284, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1749198274675395, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1749198274675560, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1749198274675950, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13937618220218904785.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1749198274676061, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13937618220218904785.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1749198274676141, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274676860, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274677323, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274677594, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274677878, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274678183, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274678543, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274678910, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274679895, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274680449, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274681028, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274681593, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274682246, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274683892, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274684108, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274684635, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274685060, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274685259, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1749198274686191, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274686458, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274686686, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1749198274686863, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1749198274687526, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274687631, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274687872, "dur": 53132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274743338, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274743910, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Debug.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274744426, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274744638, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274745403, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274745812, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274746099, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274746613, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274746807, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274747177, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 26, "ts": 1749198274741005, "dur": 6725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1749198274747805, "dur": 1878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1749198274749705, "dur": 125461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274660890, "dur": 13111, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274674019, "dur": 976, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274674005, "dur": 1030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9FB29501021D01BD.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1749198274675066, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9FB29501021D01BD.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1749198274675216, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1749198274675382, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Runtime.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1749198274675532, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Editor.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1749198274675777, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1749198274676006, "dur": 2927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274678934, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274679867, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274680371, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274680841, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274681364, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274681968, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274682613, "dur": 1472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274684085, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274684618, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274685078, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1749198274686288, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274685421, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1749198274686701, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274686804, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274687538, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274687868, "dur": 50606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274738475, "dur": 2928, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274744048, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274744425, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274745088, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274745617, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274741404, "dur": 4777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1749198274746182, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1749198274746393, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274746480, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274746614, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274746918, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1749198274746316, "dur": 3469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1749198274749829, "dur": 125356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274660915, "dur": 13099, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274674025, "dur": 579, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274674017, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9BF9A108AC9C7691.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1749198274674628, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274674626, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA1FDB39B7262DB0.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1749198274675223, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1749198274675489, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1749198274675723, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1749198274675977, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274676914, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274677449, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274677793, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274678136, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274678542, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274678997, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274679714, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274680181, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274680817, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274681341, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274682030, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274682569, "dur": 1493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274684080, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274684609, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274685062, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1749198274685225, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274685359, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1749198274686288, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274686383, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274686542, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274686681, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274686814, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274687535, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 28, "ts": 1749198274688110, "dur": 98, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274688698, "dur": 44132, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 28, "ts": 1749198274741046, "dur": 1437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274743202, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274743503, "dur": 466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274744730, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCompression.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274744805, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274745024, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Stores.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274745159, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Win32.Registry.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274745762, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274746528, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274746850, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 28, "ts": 1749198274741002, "dur": 6811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1749198274747813, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274747894, "dur": 1927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1749198274749822, "dur": 125357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274660940, "dur": 13083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274674039, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274674026, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_EDAFC568D9FA3A3B.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1749198274674292, "dur": 689, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274674290, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_33D35AB4B6B5FB5D.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1749198274675058, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1749198274675327, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SmoothNormalTool.Runtime.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1749198274675608, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1749198274676048, "dur": 3681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274679729, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274680394, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274680878, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274681434, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274681874, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274682312, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274682755, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274684096, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274684628, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274685087, "dur": 1241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274686367, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274686540, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274686686, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274686809, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274687559, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274687876, "dur": 53159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1749198274743114, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274744052, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274741051, "dur": 3289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1749198274744688, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274744768, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274745157, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274745811, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274746979, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-interlocked-l1-1-0.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274747047, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-memory-l1-1-0.dll"}}, {"pid": 12345, "tid": 29, "ts": 1749198274744388, "dur": 4882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1749198274749334, "dur": 125810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274660989, "dur": 13043, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274674047, "dur": 930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 30, "ts": 1749198274674036, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_8B7A85A19518D75E.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1749198274675019, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_8B7A85A19518D75E.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1749198274675111, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_B79360A6DA5C7A31.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1749198274675485, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1749198274675936, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13283745091630919918.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1749198274676718, "dur": 879, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Metadata.dll"}}, {"pid": 12345, "tid": 30, "ts": 1749198274676008, "dur": 3868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274679877, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274680326, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274680915, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274681531, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274682146, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274683664, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274684100, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274684625, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274685086, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274686349, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274686537, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274686680, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274686838, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274687553, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274687901, "dur": 53117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1749198274744802, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 30, "ts": 1749198274741019, "dur": 5269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1749198274746509, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1749198274746326, "dur": 3327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1749198274749709, "dur": 125460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274661044, "dur": 13074, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274674136, "dur": 464, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1749198274674122, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_15002E7141BD1652.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1749198274674624, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1749198274674622, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_566871F2B7E66C79.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1749198274675039, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274675287, "dur": 567, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1749198274675897, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5021621412459265986.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1749198274676062, "dur": 3564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274679627, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274680004, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274680538, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274681147, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274681863, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274682591, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274684079, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274684616, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274685088, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274686334, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274686525, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274686648, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274686813, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274687540, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274687877, "dur": 53152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274744360, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 31, "ts": 1749198274745208, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 31, "ts": 1749198274741030, "dur": 4560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1749198274745591, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1749198274746471, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 31, "ts": 1749198274746875, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 31, "ts": 1749198274745871, "dur": 3762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1749198274749716, "dur": 125457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274661059, "dur": 13085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274674155, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674145, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9849D89734878B5B.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674309, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674308, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_281F71B819C192F3.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674470, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674469, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_06D4D5C6893B194B.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674661, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274674660, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_50F0ACEB18AF6771.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675005, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274675062, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_50F0ACEB18AF6771.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675191, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675375, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675508, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675706, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675814, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675867, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675937, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274676018, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274676129, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274676431, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274676532, "dur": 675, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274677211, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274677337, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274677685, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274677846, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678193, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678297, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678428, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678607, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678779, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678877, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274678949, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679067, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679171, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679237, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679539, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679644, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679831, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679917, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274679991, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680094, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680157, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680218, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680280, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680395, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680490, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680575, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680659, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680774, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274680870, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681001, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681076, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681157, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681250, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681337, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681404, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681470, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681549, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681658, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681733, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681807, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681858, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681910, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274681986, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682043, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682109, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682278, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682366, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682461, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682565, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682653, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682759, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682839, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274682918, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683017, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683120, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683206, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683313, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683407, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683480, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683558, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683627, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683750, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274675625, "dur": 8325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1749198274683992, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274684066, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274684603, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274685054, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274685251, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1749198274686406, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274686667, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1749198274686812, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1749198274687447, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274687554, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274687896, "dur": 53106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274744496, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274744856, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274745665, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274746332, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.60f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 32, "ts": 1749198274741006, "dur": 5592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": ****************, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274746681, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274746745, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274746959, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274747245, "dur": 2155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1749198274749453, "dur": 125697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749198274881114, "dur": 3129, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 41876, "tid": 1115, "ts": 1749198274897752, "dur": 1977, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 41876, "tid": 1115, "ts": 1749198274899870, "dur": 3481, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 41876, "tid": 1115, "ts": 1749198274892790, "dur": 11294, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}