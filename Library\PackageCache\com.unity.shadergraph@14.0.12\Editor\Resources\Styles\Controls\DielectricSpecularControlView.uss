DielectricSpecularControlView {
    padding-left: 8px;
    padding-right: 8px;
}

DielectricSpecularControlView > #enumPanel {
    flex-direction: row;
    flex-grow: 1;
}

DielectricSpecularControlView > #enumPanel > Label {
    width: 60px;
    -unity-text-align : middle-left;
}

DielectricSpecularControlView > #enumPanel > EnumField {
    flex-grow: 1;
    margin-top: 4px;
    margin-bottom: 4px;
}

DielectricSpecularControlView > #enumPanel > EnumField .unity-base-field__input {
    flex-grow: 1;
    margin-left: 0;
    margin-right: 0;
}

DielectricSpecularControlView .unity-enum-field__text {
    margin-left: 5px;
    margin-right: 10px;
}

DielectricSpecularControlView > #sliderPanel {
    flex-direction: row;
    flex-grow: 1;
}

DielectricSpecularControlView > #sliderPanel > Label {
    width: 60px;
    -unity-text-align : middle-left;
}

DielectricSpecularControlView > #sliderPanel > Slider {
    flex-grow: 1;
    overflow:visible;
}

DielectricSpecularControlView > #sliderPanel > Slider .unity-base-field__input {
    overflow:visible;
}

DielectricSpecularControlView > #sliderPanel > FloatField {
    margin-right: 0;
    padding-right: 0;
    width: 40px;
}

DielectricSpecularControlView > #sliderPanel > FloatField > .unity-base-field__input{
    margin-left: 4px;
    margin-right: 0;
    -unity-text-align: middle-left;
}
